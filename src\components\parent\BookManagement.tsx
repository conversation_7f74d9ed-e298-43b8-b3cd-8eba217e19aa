import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  BookOpen, 
  FileText, 
  Upload,
  Clock,
  CheckCircle2,
  AlertCircle,
  Trash2
} from 'lucide-react';
import PdfViewerModal from '@/components/ui/pdf-viewer-modal';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { LoadingState } from '@/components/ui/loading-state';


interface Document {
  id: string;
  title: string;
  subject_id: string;
  class_level: string;
  processing_status: string | null;
  created_at: string;
  file_path: string;
  subjects?: { name: string };
}

interface BookManagementProps {
  onBooksUpdate?: () => void;
}

export const BookManagement = ({ onBooksUpdate }: BookManagementProps) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSubject, setSelectedSubject] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [viewerOpen, setViewerOpen] = useState(false);
  const [viewerDoc, setViewerDoc] = useState<{ id: string; file_path: string; title: string } | null>(null);
  const [deletingDocId, setDeletingDocId] = useState<string | null>(null);
  
  const { toast } = useToast();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) return;

      // Fetch subjects
      const { data: subjectsData } = await supabase
        .from('subjects')
        .select('*')
        .order('name');

      // Fetch documents
      const { data: documentsData } = await supabase
        .from('documents')
        .select(`
          *,
          subjects(name)
        `)
        .eq('user_id', user.user.id)
        .order('created_at', { ascending: false });

      setSubjects(subjectsData || []);
      setDocuments(documentsData || []);
    } catch (error: any) {
      toast({
        title: "Error fetching data",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const openDocument = (doc: Document) => {
    setViewerDoc({ id: doc.id, file_path: doc.file_path, title: doc.title });
    setViewerOpen(true);
  };

  const handleDeleteDocument = async (docId: string) => {
    setDeletingDocId(docId);
    try {
      const { data, error } = await supabase.functions.invoke('delete-document', {
        body: { document_id: docId },
      });
      if (error) throw error;
      toast({ title: 'Pages deleted', description: 'The pages and their content were processed for deletion.' });
      await fetchData();
      onBooksUpdate?.();
    } catch (e: any) {
      toast({ title: 'Delete failed', description: e.message, variant: 'destructive' });
    } finally {
      setDeletingDocId(null);
    }
  };

  const getStatusIcon = (status: string | null) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="w-4 h-4 text-green-600" />;
      case 'processing':
        return <Clock className="w-4 h-4 text-blue-600 animate-spin" />;
      default:
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
    }
  };

  const getStatusBadge = (status: string | null) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'processing':
        return <Badge variant="secondary">Processing</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  const getFilteredDocuments = () => {
    return documents.filter(doc => {
      if (selectedSubject && selectedSubject !== 'all' && doc.subject_id !== selectedSubject) return false;
      if (selectedClass && selectedClass !== 'all' && doc.class_level !== selectedClass) return false;
      return true;
    });
  };

  const getDocumentsBySubjectAndClass = () => {
    const grouped: Record<string, Record<string, Document[]>> = {};
    
    documents.forEach(doc => {
      const subject = doc.subjects?.name || 'Unknown Subject';
      const classLevel = `Class ${doc.class_level}`;
      
      if (!grouped[subject]) {
        grouped[subject] = {};
      }
      if (!grouped[subject][classLevel]) {
        grouped[subject][classLevel] = [];
      }
      
      grouped[subject][classLevel].push(doc);
    });
    
    return grouped;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">Loading pages...</div>
        </CardContent>
      </Card>
    );
  }

  const groupedDocuments = getDocumentsBySubjectAndClass();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <BookOpen className="w-5 h-5 mr-2" />
                Pages Library
              </CardTitle>
              <CardDescription>
                Organize and manage your pages by subject and class
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Filters */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <Label htmlFor="subjectFilter">Filter by Subject</Label>
              <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="All subjects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All subjects</SelectItem>
                  {subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex-1">
              <Label htmlFor="classFilter">Filter by Class</Label>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger>
                  <SelectValue placeholder="All classes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All classes</SelectItem>
                  {Array.from({ length: 12 }, (_, i) => i + 1).map((level) => (
                    <SelectItem key={level} value={level.toString()}>
                      Class {level}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {documents.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No pages uploaded yet</h3>
              <p className="text-muted-foreground mb-4">
                Upload your first pages to start organizing your educational content.
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Show filtered view if filters are applied */}
              {(selectedSubject || selectedClass) ? (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">
                    Filtered Pages ({getFilteredDocuments().length})
                  </h3>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {getFilteredDocuments().map((doc) => (
                      <Card key={doc.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => openDocument(doc)}>
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1">
                              <CardTitle className="text-lg">{doc.title}</CardTitle>
                              <CardDescription className="mt-1">
                                {doc.subjects?.name} • Class {doc.class_level}
                              </CardDescription>
                            </div>
                            <div className="flex items-center gap-2">
                              {getStatusIcon(doc.processing_status)}
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    variant="destructive"
                                    size="icon"
                                    onClick={(e) => { e.stopPropagation(); }}
                                    aria-label="Delete pages"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent onClick={(e) => e.stopPropagation()}>
                                  <AlertDialogHeader>
                                     <AlertDialogTitle>Delete these pages?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                       This will delete the page records and their content from the database. Questions used in assessments will be soft-deleted.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel onClick={(e) => e.stopPropagation()}>Cancel</AlertDialogCancel>
                                    <AlertDialogAction onClick={(e) => { e.stopPropagation(); handleDeleteDocument(doc.id); }}>Delete</AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex items-center justify-between">
                            {getStatusBadge(doc.processing_status)}
                            <span className="text-xs text-muted-foreground">
                              {new Date(doc.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ) : (
                /* Show grouped view by default */
                Object.entries(groupedDocuments).map(([subject, classes]) => (
                  <div key={subject} className="space-y-4">
                    <h3 className="text-lg font-medium border-b pb-2">{subject}</h3>
                    {Object.entries(classes).map(([classLevel, docs]) => (
                      <div key={classLevel} className="ml-4">
                        <h4 className="text-md font-medium text-muted-foreground mb-3">{classLevel}</h4>
                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {docs.map((doc) => (
                            <Card key={doc.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => openDocument(doc)}>
                              <CardHeader className="pb-3">
                                <div className="flex items-start justify-between gap-3">
                                  <div className="flex-1">
                                    <CardTitle className="text-lg">{doc.title}</CardTitle>
                                    <CardDescription className="mt-1">
                                      Added {new Date(doc.created_at).toLocaleDateString()}
                                    </CardDescription>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {getStatusIcon(doc.processing_status)}
                                    <AlertDialog>
                                      <AlertDialogTrigger asChild>
                                        <Button
                                          variant="destructive"
                                          size="icon"
                                          onClick={(e) => { e.stopPropagation(); }}
                                          aria-label="Delete pages"
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                      </AlertDialogTrigger>
                                      <AlertDialogContent onClick={(e) => e.stopPropagation()}>
                                        <AlertDialogHeader>
                                          <AlertDialogTitle>Delete these pages?</AlertDialogTitle>
                                          <AlertDialogDescription>
                                             This will delete the page records and their content from the database. Questions used in assessments will be soft-deleted.
                                          </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                          <AlertDialogCancel onClick={(e) => e.stopPropagation()}>Cancel</AlertDialogCancel>
                                          <AlertDialogAction onClick={(e) => { e.stopPropagation(); handleDeleteDocument(doc.id); }}>Delete</AlertDialogAction>
                                        </AlertDialogFooter>
                                      </AlertDialogContent>
                                    </AlertDialog>
                                  </div>
                                </div>
                              </CardHeader>
                              <CardContent className="pt-0">
                                <div className="flex items-center justify-between">
                                  {getStatusBadge(doc.processing_status)}
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ))
              )}
            </div>
          )}
        </CardContent>
      </Card>
  <PdfViewerModal
        open={viewerOpen}
        onOpenChange={setViewerOpen}
        filePath={viewerDoc?.file_path}
        title={viewerDoc?.title}
        documentId={viewerDoc?.id}
      />
      {deletingDocId && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center">
          <Card className="w-[280px]">
            <CardHeader>
              <CardTitle>Deleting pages...</CardTitle>
            </CardHeader>
            <CardContent>
              <LoadingState text="Please wait while we delete the pages." />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};